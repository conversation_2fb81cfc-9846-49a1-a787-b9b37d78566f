// src/app.ts
import 'reflect-metadata';
import { DataSource } from 'typeorm';
import { EmailRecord } from './entities/emailRecords';
import * as dotenv from 'dotenv';
dotenv.config();

export const AppDataSource = new DataSource({
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    username: process.env.DB_USERNAME || 'verifier',
    password: process.env.DB_PASSWORD || 'secret',
    database: process.env.DB_DATABASE || 'email_verifier',
    synchronize: true,
    logging: false,
    entities: [EmailRecord],
});

export const initializeDataSource = async () => {
    try {
        await AppDataSource.initialize();
        console.log('Data Source has been initialized!');
    } catch (error) {
        console.error('Error during Data Source initialization:', error);
    }
};

