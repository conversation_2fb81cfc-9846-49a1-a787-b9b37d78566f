{"name": "email-verifier", "version": "1.0.0", "main": "index.ts", "scripts": {"build": "tsc", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "ts-node src/index.ts", "start:dev": "nodemon src/index.ts", "start:prod": "node dist/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.11.0", "dotenv": "^17.2.1", "express": "^5.1.0", "pg": "^8.16.3", "reflect-metadata": "^0.2.2", "typeorm": "^0.3.25"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^24.1.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}