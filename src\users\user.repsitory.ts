// src/repositories/emailRecordRepository.ts
import { AppDataSource } from '../app';
import { EmailRecord } from '../entities/emailRecords';

export class EmailRecordRepository {
  private repo = AppDataSource.getRepository(EmailRecord);

  async findById(id: string): Promise<EmailRecord | null> {
    return this.repo.findOne({ where: { id: parseInt(id, 10) } });
  }

  async save(email: string, isValid: boolean): Promise<EmailRecord> {
    const record = this.repo.create({ email, isValid });
    return this.repo.save(record);
  }

  async findAll(): Promise<EmailRecord[]> {
    return this.repo.find();
  }
}
