import * as dns from 'dns';
import * as net from 'net';

interface MxRecord {
  exchange: string;
  priority: number;
}

interface EmailVerificationResult {
  isValid: boolean;
  isUndetermined: boolean;
  error?: Error;
}

interface EmailVerificationOptions {
  timeout?: number;
  fromEmail?: string;
}

export class EmailVerificationService {

  private readonly MAX_EMAIL_LEN = 300;
  private readonly DEFAULT_TIMEOUT = 5000;
  private readonly SMTP_PORT = 25;

  private isValidEmailFormat(email: string): boolean {
    if (email.length > this.MAX_EMAIL_LEN) {
      return false;
    }
    return /^\S+@\S+$/.test(email);
  }

  private async resolveMxRecords(domain: string): Promise<MxRecord[]> {
    return new Promise((resolve, reject) => {
      dns.resolveMx(domain, (err, addresses) => {
        if (err || !addresses || addresses.length === 0) {
          reject(err || new Error('No MX records found'));
          return;
        }

        const sortedAddresses = addresses.sort((a, b) => a.priority - b.priority);
        resolve(sortedAddresses);
      });
    });
  }

  private async verifyEmailOnServer(
    email: string,
    mxRecord: MxRecord,
    fromEmail: string,
    timeout: number
  ): Promise<EmailVerificationResult> {
    return new Promise((resolve) => {
      const conn = net.createConnection(this.SMTP_PORT, mxRecord.exchange);
      const commands = [
        `HELO ${mxRecord.exchange}`,
        `MAIL FROM: <${fromEmail}>`,
        `RCPT TO: <${email}>`
      ];

      let commandIndex = 0;
      let isResolved = false;

      const cleanup = () => {
        if (!isResolved) {
          isResolved = true;
          conn.removeAllListeners();
          conn.destroy();
        }
      };

      const resolveResult = (result: EmailVerificationResult) => {
        if (!isResolved) {
          isResolved = true;
          cleanup();
          resolve(result);
        }
      };

      conn.setEncoding('ascii');
      conn.setTimeout(timeout);

      conn.on('error', (error: Error) => {
        resolveResult({ isValid: false, isUndetermined: true, error });
      });

      conn.on('timeout', () => {
        resolveResult({ isValid: false, isUndetermined: true, error: new Error('Connection timeout') });
      });

      conn.on('connect', () => {
        conn.on('data', (data: string) => {
          const response = data.toString();

          // Check for positive responses (220, 250)
          if (this.isPositiveResponse(response)) {
            if (commandIndex < commands.length) {
              conn.write(commands[commandIndex] + '\r\n');
              commandIndex++;
            } else {
              // All commands successful - email exists
              resolveResult({ isValid: true, isUndetermined: false });
            }
          }
          // Check for email not found (550)
          else if (this.isEmailNotFoundResponse(response)) {
            resolveResult({ isValid: false, isUndetermined: false });
          }
          // Check for temporary failures or other errors
          else if (this.isTemporaryFailureResponse(response)) {
            resolveResult({ isValid: false, isUndetermined: true, error: new Error(`Temporary failure: ${response}`) });
          }
          // Unknown response
          else {
            resolveResult({ isValid: false, isUndetermined: true, error: new Error(`Unknown response: ${response}`) });
          }
        });
      });
    });
  }


  /**
   * Checks if the response indicates success (220, 250)
   */
  private isPositiveResponse(response: string): boolean {
    return /^220|^250|\n220|\n250/.test(response);
  }

  /**
   * Checks if the response indicates email not found (550)
   */
  private isEmailNotFoundResponse(response: string): boolean {
    return /^550|\n550/.test(response);
  }

  /**
   * Checks if the response indicates temporary failure (4xx codes)
   */
  private isTemporaryFailureResponse(response: string): boolean {
    return /^4\d{2}|\n4\d{2}/.test(response);
  }

  /**
   * Main email verification method
   */
  public async verifyEmail(
    email: string,
    options: EmailVerificationOptions = {}
  ): Promise<EmailVerificationResult> {
    const { timeout = this.DEFAULT_TIMEOUT, fromEmail = email } = options;

    // Validate email format
    if (!this.isValidEmailFormat(email)) {
      return { isValid: false, isUndetermined: false, error: new Error('Invalid email format') };
    }

    try {
      const domain = email.split('@')[1];
      const mxRecords = await this.resolveMxRecords(domain);

      // Try each MX server in priority order
      for (const mxRecord of mxRecords) {
        try {
          const result = await this.verifyEmailOnServer(email, mxRecord, fromEmail, timeout);

          // If we get a definitive result (valid or invalid), return it
          if (!result.isUndetermined) {
            return result;
          }

          // If undetermined, continue to next server
          console.log(`Server ${mxRecord.exchange} gave undetermined result, trying next server...`);
        } catch (error) {
          console.log(`Error with server ${mxRecord.exchange}:`, error);
          // Continue to next server
        }
      }

      // All servers gave undetermined results
      return {
        isValid: false,
        isUndetermined: true,
        error: new Error('All SMTP servers gave undetermined results')
      };

    } catch (error) {
      return {
        isValid: false,
        isUndetermined: true,
        error: error instanceof Error ? error : new Error('Unknown error occurred')
      };
    }
  }

  /**
   * Callback-style wrapper for backward compatibility
   */
  public check(
    email: string,
    callback: (error: Error | null, isValid: boolean, isUndetermined?: boolean) => void,
    timeout?: number,
    fromEmail?: string
  ): void {
    this.verifyEmail(email, { timeout, fromEmail })
      .then(result => {
        callback(result.error || null, result.isValid, result.isUndetermined);
      })
      .catch(error => {
        callback(error, false, true);
      });
  }
}
