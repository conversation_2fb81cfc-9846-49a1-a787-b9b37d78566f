import express, { Router } from 'express';
import { UserRouter } from './users/user.router';
import { EmailVerificationRouter } from './email-verification/email.verification.router';


export class BaseRouter {
    public router: Router;

    constructor() {
        this.router = express.Router();
        this.initializeRoutes()
    }

    public initializeRoutes() {
        const userRouter = new UserRouter();
        const emailVerificationRouter = new EmailVerificationRouter();

        this.router.use('/users', userRouter.router);
        this.router.use('/email-verification',emailVerificationRouter.router);
    }
}
