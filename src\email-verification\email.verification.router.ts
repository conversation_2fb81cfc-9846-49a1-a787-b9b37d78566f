import { EmailVerificationController } from "./emai.verification.controller";
import express, { Request, Response, Router } from "express";

export class EmailVerificationRouter {
    public router: Router;
    private emailVerificationController: EmailVerificationController

    constructor() {
        this.router = express.Router();
        this.emailVerificationController = new EmailVerificationController();
        this.registerRoutes();
    }

    private registerRoutes(): void {
        this.router.post('/verify',this.emailVerificationController.verifyEmail);
    }
}
