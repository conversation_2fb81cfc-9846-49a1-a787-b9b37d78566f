import { Request, Response } from "express";
import { EmailVerificationService } from "./email.verification.service";

export class EmailVerificationController {
    private emailVerificationService: EmailVerificationService;

    constructor() {
        this.emailVerificationService = new EmailVerificationService();
    }

    async verifyEmail(req: Request, res: Response){
        const { email } = req.query as {email: string};
        try {
            const result = await this.emailVerificationService.verifyEmail(email);
            return res.status(200).json(result);
        } catch (error) {
            console.error('Email verification error:', error);
            return res.status(500).json({ error: 'Internal server error' });
        }
    }

}
