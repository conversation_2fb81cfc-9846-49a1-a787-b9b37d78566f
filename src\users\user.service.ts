// src/services/userService.ts
import { EmailRecordRepository } from './user.repsitory';

export class UserService {
  private emailRecordRepository: EmailRecordRepository
  constructor() {
    this.emailRecordRepository = new EmailRecordRepository();
  }

  async getUserById(userId: string) {
    return this.emailRecordRepository.findById(userId);
  }

  async getAllUsers() {
    return this.emailRecordRepository.findAll();
  }
}
