import { UserController } from "./user.controller";

import express, { Request, Response, Router } from "express";

export class UserRouter {
    public router: Router;
    private userController: UserController;

    constructor() {
        this.router = express.Router();
        this.userController = new UserController();
        this.registerRoutes();
    }

    private registerRoutes(): void {
    this.router.post('/', this.userController.getUserById);
  }
}